<template>
  <q-table
    :rows="filteredRows"
    :columns="evaluateManagementColumns"
    row-key="id"
    :pagination="pagination"
    :rows-per-page-options="[5, 10, 15]"
    flat
    bordered
    separator="cell"
  >
    <!-- Date Column -->
    <template v-slot:body-cell-date="{ row }">
      <q-td class="text-center">{{ row.date }}</q-td>
    </template>

    <!-- Link Column -->
    <template v-slot:body-cell-link="{ row }">
      <q-td class="text-center">
        <div class="flex justify-center">
          <q-icon name="link" size="20px" class="cursor-pointer" @click="openShareDialog(row)" />
        </div>
      </q-td>
    </template>

    <!-- Actions Column -->
    <template v-slot:body-cell-actions="{ row }">
      <q-td class="text-center">
        <div class="q-gutter-x-sm flex justify-center">
          <q-btn
            dense
            unelevated
            class="view-icon"
            icon="visibility"
            @click="onClickPreview(row)"
          />
          <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
          <q-btn
            dense
            unelevated
            class="edit-graph-icon"
            icon="bar_chart"
            @click="onClickChart(row)"
          />
          <q-btn dense unelevated class="del-icon" icon="delete" @click="deleteItem(row)" />
        </div>
      </q-td>
    </template>
  </q-table>
  <ShareLinkDialog ref="shareLinkDialog" />
  <ConfirmDialog
    v-model="confirmDialogVisible"
    :title="titleDialog"
    @confirm="onConfirmDelete"
    @cancel="onCancelDelete"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import ShareLinkDialog from 'src/components/common/ShareLinkDialog.vue';
import router from 'src/router';
import { AssessmentService } from 'src/services/quiz/assessmentService';
import type { Assessment } from 'src/types/models';
import { evaluateManagementColumns } from 'src/data/table_columns';
import ConfirmDialog from '../ConfirmDialog.vue';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { QTableProps } from 'quasar';
const shareLinkDialog = ref<InstanceType<typeof ShareLinkDialog> | null>(null);
const formData = ref<Assessment[]>([]);
const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Row | null>(null);
const titleDialog = ref('');
function openShareDialog(row: Row) {
  if (shareLinkDialog.value) {
    shareLinkDialog.value.openDialog(`https://example.com/evaluate/${row.id}`, 'ตกลง');
  }
}
const rows = ref<Row[]>([]);
interface Row {
  id: number;
  name: string;
  creator?: {
    name: string;
  };
  date: string;
  link: string;
}

const filter = ref('');
const pagination = ref({ ...defaultPaginationValue });

const filteredRows = computed(() => {
  if (!filter.value) return formData.value;
  const keyword = filter.value.toLowerCase();
  return rows.value.filter(
    (row) =>
      row.name.toLowerCase().includes(keyword) || row.creator?.name.toLowerCase().includes(keyword),
  );
});
// stub functions
async function onClickPreview(row: Row) {
  await router.push({ path: '/evaluate/management/preview-evaluate', query: { id: row.id } });
}
async function onClickEdit(row: Row) {
  try {
    const resolveRoute = router.resolve({
      name: 'evaluate-id',
      params: { id: row.id.toString() },
      query: { mode: 'edit' },
      hash: '#questions',
    });
    await router.push(resolveRoute);
  } catch (error) {
    console.error('Navigation to edit view failed:', error);
    return;
  }
}

async function onClickChart(row: Row) {
  try {
    const resolveRoute = router.resolve({
      name: 'evaluate-id',
      params: { id: row.id.toString() },
      query: { mode: 'edit' },
      hash: '#replies',
    });
    await router.push(resolveRoute);
    console.log('ดูกราฟ', row);
  } catch (error) {
    console.error('Navigation to graph view failed:', error);
    return;
  }
}

function deleteItem(row: Row) {
  selectedRowToDelete.value = row;
  titleDialog.value = `ยืนยันการลบแบบประเมิน ${row.name}`;
  confirmDialogVisible.value = true;
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService('evaluate').deleteOne(selectedRowToDelete.value.id);
    // await form.deleteForm(selectedRowToDelete.value.id);
    await fetchData(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

async function fetchData(pagination: QTableProps['pagination']) {
  const res = await new AssessmentService('evaluate').fetchAll(pagination);
  formData.value = res.data;
}

onMounted(async () => {
  await fetchData(pagination.value);
});
</script>

<style scoped>
.view-icon {
  background-color: #39303d;
  color: white;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: white;
  border-radius: 12px;
}

.del-icon {
  background-color: #ab2433;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
