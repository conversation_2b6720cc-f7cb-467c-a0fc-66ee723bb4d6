<template>
  <q-page padding>
    <div class="row items-center justify-between q-mb-md">
      <div class="body">การจัดการแบบสอบถาม</div>
      <div class="row items-center q-gutter-sm">
        <SearchBar />
        <q-btn
          label="สร้าง"
          icon="add"
          class="text-white"
          color="accent"
          s
          @click="onClickCreate"
        />
      </div>
    </div>
    <EvaluateTable />
  </q-page>
</template>

<script setup lang="ts">
import EvaluateTable from 'src/components/evaluate/EvaluateTable.vue';
import SearchBar from 'src/components/SearchBar.vue';
import router from 'src/router';
import { useQuasar } from 'quasar';
import { AssessmentService } from 'src/services/quiz/assessmentService';
import { useAuthStore } from 'src/stores/auth';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';

const $q = useQuasar();

async function onClickCreate() {
  try {
    const user = useAuthStore().getCurrentUser();
    const evaluateFormStore = useEvaluateFormStore();

    console.log('Creating new assessment using AssessmentService...');
    console.log('Current user:', user);
    console.log('User ID:', user?.id);

    // Validate user authentication
    if (!user || !user.id) {
      console.error('User not authenticated or missing ID');
      console.error('User object:', user);
      $q.notify({
        type: 'negative',
        message: 'กรุณาเข้าสู่ระบบก่อนสร้างแบบประเมิน',
        position: 'bottom',
      });
      return;
    }

    // Additional validation for user ID
    if (typeof user.id !== 'number' || user.id <= 0) {
      console.error('Invalid user ID:', user.id);
      $q.notify({
        type: 'negative',
        message: 'รหัสผู้ใช้ไม่ถูกต้อง กรุณาเข้าสู่ระบบใหม่',
        position: 'bottom',
      });
      return;
    }

    const payload = {
      creatorUserId: user.id,
      programId: 1,
      type: 'FORM' as const,
    };

    console.log('Assessment creation payload:', payload);

    // Use AssessmentService which uses the correct /evaluate/assessments endpoint
    const res = await new AssessmentService('evaluate').createOne(payload);

    console.log('Assessment created successfully:', {
      id: res.id,
      itemBlocks: res.itemBlocks?.map((block) => ({
        id: block.id,
        type: block.type,
        assessmentId: block.assessmentId,
      })),
    });

    // Store the created assessment immediately in the evaluate form store
    evaluateFormStore.currentAssessment = res;

    await router.push({
      name: 'evaluate-id',
      query: { mode: 'edit' },
      params: { id: res.id },
      hash: '#questions',
    });
  } catch (error) {
    console.error('Failed to create assessment:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      response: (error as { response?: { data?: unknown } })?.response?.data,
      status: (error as { response?: { status?: number } })?.response?.status,
    });

    $q.notify({
      type: 'negative',
      message: 'ไม่สามารถสร้างแบบประเมินได้ กรุณาลองใหม่อีกครั้ง',
      position: 'bottom',
    });
  }
}
</script>

<style scoped></style>
