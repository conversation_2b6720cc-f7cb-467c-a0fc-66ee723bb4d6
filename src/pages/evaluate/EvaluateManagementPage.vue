<template>
  <q-page padding>
    <div class="row items-center justify-between q-mb-md">
      <div class="body">การจัดการแบบสอบถาม</div>
      <div class="row items-center q-gutter-sm">
        <SearchBar />
        <q-btn
          label="สร้าง"
          icon="add"
          class="text-white"
          color="accent"
          s
          @click="onClickCreate"
        />
      </div>
    </div>
    <EvaluateTable />
  </q-page>
</template>

<script setup lang="ts">
import EvaluateTable from 'src/components/evaluate/EvaluateTable.vue';
import SearchBar from 'src/components/SearchBar.vue';
import router from 'src/router';
import { AssessmentService } from 'src/services/quiz/assessmentService';
import { useAuthStore } from 'src/stores/auth';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';

async function onClickCreate() {
  try {
    const user = useAuthStore().getCurrentUser();
    const evaluateFormStore = useEvaluateFormStore();

    console.log('Creating new assessment using AssessmentService...');

    // Use AssessmentService which uses the correct /evaluate/assessments endpoint
    const res = await new AssessmentService('evaluate').createOne({
      creatorUserId: user?.id || 1,
      programId: 1,
      type: 'EVALUATE',
    });

    // Store the created assessment immediately in the evaluate form store
    evaluateFormStore.currentAssessment = res;

    await router.push({
      name: 'evaluate-id',
      query: { mode: 'edit' },
      params: { id: res.id },
      hash: '#questions',
    });
  } catch (error) {
    console.error('Failed to create assessment:', error);
  }
}
</script>

<style scoped></style>
